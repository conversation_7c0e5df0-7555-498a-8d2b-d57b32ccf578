import { useState, useEffect } from 'react';
import { gsap } from 'gsap';

const TestimonialCarousel = ({ testimonials = [] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Default testimonials if none provided
  const defaultTestimonials = [
    {
      id: 1,
      name: "Hanson Deck",
      title: "UX Designer, Research",
      company: "HubSpot",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/hubspot.svg",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
      rating: 5,
      text: "<PERSON><PERSON>s quis nunc sollicitudin leo convallis lectus tempor diam elementum ellus diam lectus Mauris eu ultrices libero non euismod arcu orci nulla eleiend libero sed maximus bland diam malesuada phareoa sodalez egestas mi arcu ex congue ax mattis neque nlemenum quam."
    },
    {
      id: 2,
      name: "Sarah Johnson",
      title: "Product Manager",
      company: "Google",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/google-icon.svg",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      rating: 5,
      text: "Outstanding work quality and attention to detail. The team delivered beyond our expectations and helped us achieve remarkable results in our digital transformation journey."
    },
    {
      id: 3,
      name: "Michael Chen",
      title: "Creative Director",
      company: "Adobe",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/adobe-1.svg",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
      rating: 5,
      text: "Exceptional creativity and professional execution. Their innovative approach to design and branding has significantly elevated our brand presence in the market."
    }
  ];

  const testimonialData = testimonials.length > 0 ? testimonials : defaultTestimonials;

  const nextTestimonial = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    
    gsap.to('.testimonial-content', {
      opacity: 0,
      x: -50,
      duration: 0.3,
      onComplete: () => {
        setCurrentIndex((prev) => (prev + 1) % testimonialData.length);
        gsap.fromTo('.testimonial-content', 
          { opacity: 0, x: 50 },
          { 
            opacity: 1, 
            x: 0, 
            duration: 0.3,
            onComplete: () => setIsAnimating(false)
          }
        );
      }
    });
  };

  const prevTestimonial = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    
    gsap.to('.testimonial-content', {
      opacity: 0,
      x: 50,
      duration: 0.3,
      onComplete: () => {
        setCurrentIndex((prev) => (prev - 1 + testimonialData.length) % testimonialData.length);
        gsap.fromTo('.testimonial-content', 
          { opacity: 0, x: -50 },
          { 
            opacity: 1, 
            x: 0, 
            duration: 0.3,
            onComplete: () => setIsAnimating(false)
          }
        );
      }
    });
  };

  const currentTestimonial = testimonialData[currentIndex];

  // Auto-play functionality
  useEffect(() => {
    const interval = setInterval(() => {
      nextTestimonial();
    }, 5000); // Change every 5 seconds

    return () => clearInterval(interval);
  }, [currentIndex, isAnimating]);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <svg
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <div className="relative w-full max-w-7xl mx-auto overflow-hidden">
      {/* Navigation Arrows - Smaller square buttons with simple arrows */}
      <button
        onClick={prevTestimonial}
        disabled={isAnimating}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg hover:bg-black hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center group border border-gray-200/50"
      >
        <svg className="w-4 h-4 text-gray-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 24 24">
          <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
        </svg>
      </button>

      <button
        onClick={nextTestimonial}
        disabled={isAnimating}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg hover:bg-black hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center group border border-gray-200/50"
      >
        <svg className="w-4 h-4 text-gray-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
        </svg>
      </button>

      {/* Carousel Container with side cards visible */}
      <div className="relative flex items-center justify-center">
        {/* Previous Card (Partial) */}
        <div className="hidden lg:block absolute left-0 top-0 w-1/4 h-full z-5 opacity-30 scale-90 transform -translate-x-8">
          {testimonialData[(currentIndex - 1 + testimonialData.length) % testimonialData.length] && (
            <div className="bg-gradient-to-br from-stone-50 via-amber-50/30 to-stone-100 rounded-3xl p-4 shadow-md border border-stone-200/50 h-full">
              <div className="grid grid-cols-12 gap-4 items-center h-full">
                <div className="col-span-4">
                  <div className="w-16 h-20 rounded-2xl overflow-hidden">
                    <img
                      src={testimonialData[(currentIndex - 1 + testimonialData.length) % testimonialData.length].image}
                      alt=""
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                <div className="col-span-8">
                  <div className="text-xs text-stone-600 line-clamp-3">
                    {testimonialData[(currentIndex - 1 + testimonialData.length) % testimonialData.length].text.substring(0, 100)}...
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Next Card (Partial) */}
        <div className="hidden lg:block absolute right-0 top-0 w-1/4 h-full z-5 opacity-30 scale-90 transform translate-x-8">
          {testimonialData[(currentIndex + 1) % testimonialData.length] && (
            <div className="bg-gradient-to-br from-stone-50 via-amber-50/30 to-stone-100 rounded-3xl p-4 shadow-md border border-stone-200/50 h-full">
              <div className="grid grid-cols-12 gap-4 items-center h-full">
                <div className="col-span-4">
                  <div className="w-16 h-20 rounded-2xl overflow-hidden">
                    <img
                      src={testimonialData[(currentIndex + 1) % testimonialData.length].image}
                      alt=""
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                <div className="col-span-8">
                  <div className="text-xs text-stone-600 line-clamp-3">
                    {testimonialData[(currentIndex + 1) % testimonialData.length].text.substring(0, 100)}...
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Main Carousel Container */}
        <div className="relative bg-gradient-to-br from-stone-50 via-amber-50/30 to-stone-100 rounded-3xl p-4 md:p-6 lg:p-8 shadow-lg border border-stone-200/50 w-full max-w-4xl mx-auto z-10">
          {/* Very subtle background texture */}
          <div className="absolute inset-0 opacity-[0.015]">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.05'%3E%3Ccircle cx='5' cy='5' r='0.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          {/* Content - Centered with reduced height */}
          <div className="testimonial-content relative z-5 flex items-center justify-center min-h-[300px]">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-12 items-center w-full max-w-5xl mx-auto">
              {/* Profile Image - Smaller and centered */}
              <div className="lg:col-span-4 flex justify-center">
              <div className="relative">
                <div className="w-48 h-60 md:w-52 md:h-64 lg:w-56 lg:h-72 rounded-3xl overflow-hidden shadow-md border border-stone-200/30">
                  <img
                    src={currentTestimonial.image}
                    alt={currentTestimonial.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>

              {/* Testimonial Content - Centered */}
              <div className="lg:col-span-8 text-center lg:text-left relative pl-0 lg:pl-8">
              {/* Large Quote Mark - positioned exactly like in design */}
              <div className="absolute -top-6 -right-2 text-[8rem] text-stone-300/30 font-serif leading-none z-0 select-none">"</div>

                {/* Stars - centered on mobile, left on desktop */}
                <div className="flex justify-center lg:justify-start mb-6 relative z-10">
                  {renderStars(currentTestimonial.rating)}
                </div>

                {/* Quote Text - centered with reduced spacing */}
                <div className="relative mb-6 z-10">
                  <p className="text-stone-700 text-lg md:text-xl lg:text-xl leading-relaxed font-normal tracking-wide">
                    {currentTestimonial.text}
                  </p>
                </div>

                {/* Author Info and Company Logo - centered layout with reduced spacing */}
                <div className="flex flex-col lg:flex-row lg:items-end lg:justify-between relative z-10">
                  <div className="mb-3 lg:mb-0 text-center lg:text-left">
                    <h4 className="text-stone-800 font-bold text-lg mb-1">
                      {currentTestimonial.name}
                    </h4>
                    <p className="text-stone-600 text-sm font-medium">
                      {currentTestimonial.title}
                    </p>
                  </div>

                  {/* Company Logo - centered on mobile, right on desktop */}
                  {currentTestimonial.companyLogo && (
                    <div className="flex justify-center lg:justify-end">
                      <div className="opacity-50">
                        <img
                          src={currentTestimonial.companyLogo}
                          alt={currentTestimonial.company}
                          className="h-10 w-auto grayscale hover:grayscale-0 transition-all duration-300"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dots Indicator */}
      <div className="flex justify-center mt-8 space-x-3">
        {testimonialData.map((_, index) => (
          <button
            key={index}
            onClick={() => {
              if (!isAnimating && index !== currentIndex) {
                setIsAnimating(true);
                gsap.to('.testimonial-content', {
                  opacity: 0,
                  scale: 0.95,
                  duration: 0.2,
                  onComplete: () => {
                    setCurrentIndex(index);
                    gsap.fromTo('.testimonial-content',
                      { opacity: 0, scale: 0.95 },
                      {
                        opacity: 1,
                        scale: 1,
                        duration: 0.2,
                        onComplete: () => setIsAnimating(false)
                      }
                    );
                  }
                });
              }
            }}
            className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
              index === currentIndex
                ? 'bg-gray-600'
                : 'bg-gray-300 hover:bg-gray-400'
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export { TestimonialCarousel };
