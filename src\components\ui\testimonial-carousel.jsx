import { useState, useEffect } from 'react';
import { gsap } from 'gsap';

const TestimonialCarousel = ({ testimonials = [] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Default testimonials if none provided
  const defaultTestimonials = [
    {
      id: 1,
      name: "Hanson Deck",
      title: "UX Designer, Research",
      company: "HubSpot",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/hubspot.svg",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
      rating: 5,
      text: "<PERSON><PERSON>s quis nunc sollicitudin leo convallis lectus tempor diam elementum ellus diam lectus Mauris eu ultrices libero non euismod arcu orci nulla eleiend libero sed maximus bland diam malesuada phareoa sodalez egestas mi arcu ex congue ax mattis neque nlemenum quam."
    },
    {
      id: 2,
      name: "Sarah Johnson",
      title: "Product Manager",
      company: "Google",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/google-icon.svg",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      rating: 5,
      text: "Outstanding work quality and attention to detail. The team delivered beyond our expectations and helped us achieve remarkable results in our digital transformation journey."
    },
    {
      id: 3,
      name: "Michael Chen",
      title: "Creative Director",
      company: "Adobe",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/adobe-1.svg",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
      rating: 5,
      text: "Exceptional creativity and professional execution. Their innovative approach to design and branding has significantly elevated our brand presence in the market."
    }
  ];

  const testimonialData = testimonials.length > 0 ? testimonials : defaultTestimonials;

  const nextTestimonial = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    
    gsap.to('.testimonial-content', {
      opacity: 0,
      x: -50,
      duration: 0.3,
      onComplete: () => {
        setCurrentIndex((prev) => (prev + 1) % testimonialData.length);
        gsap.fromTo('.testimonial-content', 
          { opacity: 0, x: 50 },
          { 
            opacity: 1, 
            x: 0, 
            duration: 0.3,
            onComplete: () => setIsAnimating(false)
          }
        );
      }
    });
  };

  const prevTestimonial = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    
    gsap.to('.testimonial-content', {
      opacity: 0,
      x: 50,
      duration: 0.3,
      onComplete: () => {
        setCurrentIndex((prev) => (prev - 1 + testimonialData.length) % testimonialData.length);
        gsap.fromTo('.testimonial-content', 
          { opacity: 0, x: -50 },
          { 
            opacity: 1, 
            x: 0, 
            duration: 0.3,
            onComplete: () => setIsAnimating(false)
          }
        );
      }
    });
  };

  const currentTestimonial = testimonialData[currentIndex];

  // Auto-play functionality
  useEffect(() => {
    const interval = setInterval(() => {
      nextTestimonial();
    }, 5000); // Change every 5 seconds

    return () => clearInterval(interval);
  }, [currentIndex, isAnimating]);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <svg
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <div className="relative w-full max-w-6xl mx-auto">
      {/* Main Carousel Container */}
      <div className="relative bg-gradient-to-br from-slate-50 via-slate-100 to-slate-200 rounded-3xl p-8 md:p-12 shadow-2xl overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(90deg, rgba(100, 116, 139, 0.1) 1px, transparent 1px),
              linear-gradient(rgba(100, 116, 139, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '40px 40px'
          }}></div>
        </div>

        {/* Navigation Arrows */}
        <button
          onClick={prevTestimonial}
          disabled={isAnimating}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center group"
        >
          <svg className="w-6 h-6 text-slate-600 group-hover:text-slate-800 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <button
          onClick={nextTestimonial}
          disabled={isAnimating}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center group"
        >
          <svg className="w-6 h-6 text-slate-600 group-hover:text-slate-800 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>

        {/* Content */}
        <div className="testimonial-content relative z-5">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
            {/* Profile Image */}
            <div className="lg:col-span-3 flex justify-center lg:justify-start">
              <div className="relative">
                <div className="w-32 h-32 md:w-40 md:h-40 rounded-2xl overflow-hidden shadow-xl">
                  <img
                    src={currentTestimonial.image}
                    alt={currentTestimonial.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                {/* Decorative ring */}
                <div className="absolute -inset-2 rounded-2xl border-2 border-slate-300/30"></div>
              </div>
            </div>

            {/* Testimonial Content */}
            <div className="lg:col-span-9 text-center lg:text-left">
              {/* Stars */}
              <div className="flex justify-center lg:justify-start mb-6">
                {renderStars(currentTestimonial.rating)}
              </div>

              {/* Quote */}
              <div className="relative mb-8">
                {/* Large Quote Mark */}
                <div className="absolute -top-4 -left-2 text-6xl text-slate-300 font-serif leading-none">"</div>
                <div className="absolute -bottom-8 -right-2 text-6xl text-slate-300 font-serif leading-none rotate-180">"</div>
                
                <p className="text-slate-700 text-lg md:text-xl leading-relaxed relative z-10 px-4">
                  {currentTestimonial.text}
                </p>
              </div>

              {/* Author Info */}
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div className="mb-4 lg:mb-0">
                  <h4 className="text-slate-800 font-bold text-xl mb-1">
                    {currentTestimonial.name}
                  </h4>
                  <p className="text-slate-600 text-sm">
                    {currentTestimonial.title}
                  </p>
                </div>

                {/* Company Logo */}
                {currentTestimonial.companyLogo && (
                  <div className="flex justify-center lg:justify-end">
                    <div className="bg-white/60 backdrop-blur-sm rounded-lg px-4 py-2 shadow-md">
                      <img
                        src={currentTestimonial.companyLogo}
                        alt={currentTestimonial.company}
                        className="h-8 w-auto opacity-70"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center mt-8 space-x-2">
          {testimonialData.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                if (!isAnimating && index !== currentIndex) {
                  setIsAnimating(true);
                  gsap.to('.testimonial-content', {
                    opacity: 0,
                    scale: 0.95,
                    duration: 0.2,
                    onComplete: () => {
                      setCurrentIndex(index);
                      gsap.fromTo('.testimonial-content', 
                        { opacity: 0, scale: 0.95 },
                        { 
                          opacity: 1, 
                          scale: 1, 
                          duration: 0.2,
                          onComplete: () => setIsAnimating(false)
                        }
                      );
                    }
                  });
                }
              }}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-slate-600 scale-125' 
                  : 'bg-slate-300 hover:bg-slate-400'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export { TestimonialCarousel };
